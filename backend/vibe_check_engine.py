"""
Vibe Check Engine - AI-powered communication analysis module.

This module provides sophisticated analysis of team chat messages using OpenAI's API,
grounded in Nonviolent Communication and the Dignity Index principles.
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from openai import OpenAI

from dotenv import load_dotenv
load_dotenv()  # reads .env into os.environ

# Configure logging
logger = logging.getLogger(__name__)

# Debug flag for OpenAI API interactions
DEBUG_PROMPT = os.getenv("DEBUG_PROMPT", "false").lower() == "true"

class VibeCheckEngine:
    """
    AI-powered communication analysis engine using OpenAI's API.
    
    Analyzes team chat messages for emotional tone, conflict markers,
    and provides actionable recommendations for improving harmony.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Vibe Check Engine.

        Args:
            api_key: OpenAI API key. If not provided, will read from OPENAI_API_KEY environment variable.
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Please set OPENAI_API_KEY environment variable or pass api_key parameter.")

        # Initialize the OpenAI client
        self.client = OpenAI(api_key=self.api_key)

        # System prompt grounded in Nonviolent Communication and Dignity Index
        self.system_prompt = (
            "You are a communication-analysis assistant grounded in Nonviolent Communication and the Dignity Index. "
            "Given a list of team chat messages, evaluate overall emotional tone, identify any conflict markers, "
            "and suggest actionable recommendations to improve harmony."
        )
    
    def analyze_messages(self, messages: List[str]) -> Dict[str, Any]:
        """
        Analyze a list of team chat messages for emotional tone and conflict markers.
        
        Args:
            messages: List of chat messages to analyze
            
        Returns:
            Dictionary with analysis results containing:
            - summary: string description of the overall communication
            - tone: one of ["harmony", "neutral", "tension"]
            - warnings: array of strings (potential conflict markers)
            - recommendations: array of strings (actionable suggestions)
        """
        if not messages:
            logger.info("No messages provided for analysis")
            return {
                "summary": "No messages to analyze.",
                "tone": "neutral",
                "warnings": [],
                "recommendations": ["Add some messages to get meaningful analysis."]
            }
        
        try:
            logger.info(f"Analyzing {len(messages)} messages with OpenAI API")
            
            # Prepare the user prompt with the messages
            user_prompt = f"Analyze these messages: {json.dumps(messages)}"
            
            # Make the API call to OpenAI
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt + "\n\nPlease respond in strict JSON format with the following keys: summary (string), tone (one of 'harmony', 'neutral', 'tension'), warnings (array of strings for potential conflict markers), recommendations (array of strings for actionable suggestions)."}
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=1000
            )
            
            # Extract the response content
            response_content = response.choices[0].message.content.strip()

            # Debug logging for OpenAI response
            if DEBUG_PROMPT:
                logger.debug(f"Raw OpenAI API response: {response_content}")
                logger.debug(f"Response metadata - Model: {response.model}, Usage: {response.usage}")

            # Parse the JSON response
            try:
                analysis_result = json.loads(response_content)
                
                # Validate the required keys are present
                required_keys = ["summary", "tone", "warnings", "recommendations"]
                for key in required_keys:
                    if key not in analysis_result:
                        logger.warning(f"Missing required key '{key}' in OpenAI response")
                        analysis_result[key] = self._get_default_value(key)
                
                # Validate tone value
                valid_tones = ["harmony", "neutral", "tension"]
                if analysis_result["tone"] not in valid_tones:
                    logger.warning(f"Invalid tone value: {analysis_result['tone']}. Defaulting to 'neutral'")
                    analysis_result["tone"] = "neutral"
                
                # Ensure warnings and recommendations are lists
                if not isinstance(analysis_result["warnings"], list):
                    analysis_result["warnings"] = []
                if not isinstance(analysis_result["recommendations"], list):
                    analysis_result["recommendations"] = []
                
                logger.info(f"Analysis completed successfully. Tone: {analysis_result['tone']}")
                return analysis_result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response from OpenAI: {e}")
                logger.error(f"Response content: {response_content}")
                return self._get_fallback_response("Failed to parse AI response")
                
        except Exception as api_error:
            # Handle various OpenAI API errors
            error_message = str(api_error)
            if "authentication" in error_message.lower() or "api key" in error_message.lower():
                logger.error("OpenAI API authentication failed. Check your API key.")
                return self._get_fallback_response("Authentication failed with OpenAI API")
            elif "rate limit" in error_message.lower():
                logger.error("OpenAI API rate limit exceeded")
                return self._get_fallback_response("API rate limit exceeded. Please try again later.")
            elif "openai" in error_message.lower() or "api" in error_message.lower():
                logger.error(f"OpenAI API error: {api_error}")
                return self._get_fallback_response("OpenAI API error occurred")
            else:
                # Handle any other exception as unexpected error
                logger.error(f"Unexpected error during message analysis: {api_error}")
                return self._get_fallback_response("Unexpected error occurred during analysis")
    
    def _get_default_value(self, key: str) -> Any:
        """Get default value for missing keys in API response."""
        defaults = {
            "summary": "Analysis completed but summary not available.",
            "tone": "neutral",
            "warnings": [],
            "recommendations": []
        }
        return defaults.get(key, "")
    
    def _get_fallback_response(self, error_message: str) -> Dict[str, Any]:
        """Generate a fallback response when API calls fail."""
        return {
            "summary": f"Unable to complete AI analysis: {error_message}",
            "tone": "neutral",
            "warnings": ["AI analysis unavailable"],
            "recommendations": ["Please check system configuration and try again."]
        }


# Convenience function for direct usage
def analyze_messages(messages: List[str]) -> Dict[str, Any]:
    """
    Convenience function to analyze messages without instantiating the class.

    Args:
        messages: List of chat messages to analyze

    Returns:
        Dictionary with analysis results
    """
    try:
        engine = VibeCheckEngine()
        return engine.analyze_messages(messages)
    except ValueError as e:
        logger.error(f"Failed to initialize VibeCheckEngine: {e}")
        return {
            "summary": "Unable to initialize AI analysis engine. Please check OpenAI API key configuration.",
            "tone": "neutral",
            "warnings": ["AI analysis unavailable - missing API key"],
            "recommendations": ["Set OPENAI_API_KEY environment variable to enable AI analysis."]
        }


# Example usage and testing
if __name__ == "__main__":
    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Test messages
    test_messages = [
        "Hey team, great work on the project!",
        "I'm not sure about this approach, it seems risky.",
        "Thanks for the feedback, let's discuss alternatives.",
        "This is completely wrong and needs to be fixed immediately!",
        "I appreciate everyone's input on this."
    ]
    
    try:
        result = analyze_messages(test_messages)
        print("Analysis Result:")
        print(json.dumps(result, indent=2))
    except Exception as e:
        print(f"Error during testing: {e}")
