
import logging
import re
from flask import Flask, request, jsonify
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class ToneAnalyzer:
    """Analyzes communication tone to detect tension or harmony."""
    
    def __init__(self):
        # Words/phrases that indicate tension or conflict
        self.tension_indicators = {
            'high': ['angry', 'furious', 'hate', 'terrible', 'awful', 'disgusting', 'stupid', 'idiot', 'wrong', 'never'],
            'medium': ['disagree', 'no', 'but', 'however', 'unfortunately', 'problem', 'issue', 'concern', 'worried'],
            'low': ['maybe', 'perhaps', 'might', 'could be', 'not sure', 'question']
        }
        
        # Words/phrases that indicate harmony or positive communication
        self.harmony_indicators = {
            'high': ['love', 'excellent', 'amazing', 'perfect', 'wonderful', 'fantastic', 'agree', 'yes', 'absolutely'],
            'medium': ['good', 'nice', 'thanks', 'appreciate', 'understand', 'makes sense', 'helpful', 'support'],
            'low': ['okay', 'fine', 'sure', 'alright', 'sounds good']
        }
    
    def analyze_messages(self, messages: List[str]) -> Dict[str, Any]:
        """Analyze a list of messages and return tone summary."""
        if not messages:
            return {
                'summary': 'No messages to analyze.',
                'tone': 'neutral',
                'tension_score': 0,
                'harmony_score': 0,
                'details': []
            }
        
        total_tension = 0
        total_harmony = 0
        message_details = []
        
        for i, message in enumerate(messages):
            if not isinstance(message, str):
                continue
                
            message_lower = message.lower()
            
            # Calculate tension score for this message
            tension_score = self._calculate_score(message_lower, self.tension_indicators)
            
            # Calculate harmony score for this message
            harmony_score = self._calculate_score(message_lower, self.harmony_indicators)
            
            total_tension += tension_score
            total_harmony += harmony_score
            
            message_details.append({
                'message_index': i + 1,
                'tension_score': tension_score,
                'harmony_score': harmony_score,
                'preview': message[:50] + '...' if len(message) > 50 else message
            })
        
        # Normalize scores based on number of messages
        avg_tension = total_tension / len(messages) if messages else 0
        avg_harmony = total_harmony / len(messages) if messages else 0
        
        # Determine overall tone
        tone, summary = self._generate_summary(avg_tension, avg_harmony, len(messages))
        
        return {
            'summary': summary,
            'tone': tone,
            'tension_score': round(avg_tension, 2),
            'harmony_score': round(avg_harmony, 2),
            'message_count': len(messages),
            'details': message_details
        }
    
    def _calculate_score(self, text: str, indicators: Dict[str, List[str]]) -> float:
        """Calculate score based on presence of indicator words."""
        score = 0
        
        for level, words in indicators.items():
            for word in words:
                # Count occurrences of each indicator word
                count = len(re.findall(r'\b' + re.escape(word) + r'\b', text))
                if level == 'high':
                    score += count * 3
                elif level == 'medium':
                    score += count * 2
                else:  # low
                    score += count * 1
        
        return score
    
    def _generate_summary(self, tension: float, harmony: float, message_count: int) -> tuple:
        """Generate plain English summary of communication tone."""
        
        if tension > harmony * 1.5:
            if tension > 5:
                tone = 'highly_tense'
                summary = f"The communication shows significant tension across {message_count} messages. " \
                         f"There are clear signs of conflict, disagreement, or negative sentiment. " \
                         f"The conversation may benefit from a more collaborative approach."
            elif tension > 2:
                tone = 'moderately_tense'
                summary = f"The communication shows moderate tension in {message_count} messages. " \
                         f"There are some signs of disagreement or concern that should be addressed."
            else:
                tone = 'slightly_tense'
                summary = f"The communication shows mild tension across {message_count} messages. " \
                         f"There are minor signs of disagreement but nothing major."
        
        elif harmony > tension * 1.5:
            if harmony > 5:
                tone = 'highly_harmonious'
                summary = f"The communication is very positive and harmonious across {message_count} messages. " \
                         f"There are strong signs of agreement, collaboration, and positive sentiment."
            elif harmony > 2:
                tone = 'moderately_harmonious'
                summary = f"The communication shows good harmony in {message_count} messages. " \
                         f"There are positive signs of collaboration and agreement."
            else:
                tone = 'slightly_harmonious'
                summary = f"The communication is generally positive across {message_count} messages " \
                         f"with some signs of agreement and collaboration."
        
        else:
            tone = 'neutral'
            summary = f"The communication appears neutral across {message_count} messages. " \
                     f"There's a balanced mix of different sentiments without strong indicators " \
                     f"of either significant tension or harmony."
        
        return tone, summary

# Initialize tone analyzer
analyzer = ToneAnalyzer()

@app.route('/analyze', methods=['POST'])
def analyze_communication():
    """Analyze communication tone from an array of messages."""
    try:
        # Validate request content type
        if not request.is_json:
            logger.warning("Request received without JSON content type")
            return jsonify({
                'error': 'Content-Type must be application/json'
            }), 400
        
        # Get JSON data
        data = request.get_json()
        
        # Validate that data is provided
        if data is None:
            logger.warning("No JSON data provided in request")
            return jsonify({
                'error': 'No JSON data provided'
            }), 400
        
        # Validate that data is a list
        if not isinstance(data, list):
            logger.warning(f"Invalid data type received: {type(data)}")
            return jsonify({
                'error': 'Expected an array of messages'
            }), 400
        
        # Log the analysis request
        logger.info(f"Analyzing {len(data)} messages")
        
        # Perform tone analysis
        result = analyzer.analyze_messages(data)
        
        logger.info(f"Analysis complete: tone={result['tone']}, "
                   f"tension={result['tension_score']}, harmony={result['harmony_score']}")
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        return jsonify({
            'error': 'Internal server error occurred during analysis'
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'tone-analyzer'}), 200

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(405)
def method_not_allowed(error):
    """Handle 405 errors."""
    return jsonify({'error': 'Method not allowed'}), 405

if __name__ == '__main__':
    logger.info("Starting Flask application...")
    app.run(debug=True, host='0.0.0.0', port=5008)
