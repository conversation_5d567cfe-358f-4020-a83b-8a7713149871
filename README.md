# Vibe Check API

A minimal Flask API that analyzes communication tone to detect tension or harmony in message exchanges.

## Features

- **POST /analyze**: Accepts a JSON array of messages and returns a tone analysis
- **GET /health**: Health check endpoint
- Comprehensive error handling and logging
- Tone analysis with tension and harmony scoring

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python app.py
```

The API will be available at `http://localhost:5000`

## Usage

### Analyze Messages

**Endpoint:** `POST /analyze`

**Request Body:**
```json
[
  "I really appreciate your help with this project!",
  "This is completely wrong and needs to be fixed immediately.",
  "Thanks for the feedback, I'll make those changes."
]
```

**Response:**
```json
{
  "summary": "The communication shows moderate tension in 3 messages. There are some signs of disagreement or concern that should be addressed.",
  "tone": "moderately_tense",
  "tension_score": 2.5,
  "harmony_score": 1.8,
  "message_count": 3,
  "details": [
    {
      "message_index": 1,
      "tension_score": 0,
      "harmony_score": 2,
      "preview": "I really appreciate your help with this project!"
    },
    {
      "message_index": 2,
      "tension_score": 5,
      "harmony_score": 0,
      "preview": "This is completely wrong and needs to be fixed imm..."
    },
    {
      "message_index": 3,
      "tension_score": 0,
      "harmony_score": 3,
      "preview": "Thanks for the feedback, I'll make those changes."
    }
  ]
}
```

### Health Check

**Endpoint:** `GET /health`

**Response:**
```json
{
  "status": "healthy",
  "service": "tone-analyzer"
}
```

## Error Handling

The API includes comprehensive error handling for:
- Invalid JSON format
- Missing request data
- Wrong data types
- Internal server errors

All errors return appropriate HTTP status codes and descriptive error messages.

## Logging

The application logs all requests and analysis results for monitoring and debugging purposes.
