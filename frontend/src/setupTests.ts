import '@testing-library/jest-dom';

// Mock fetch globally for all tests
global.fetch = jest.fn();

// Reset fetch mock before each test
beforeEach(() => {
  (fetch as jest.Mock).mockClear();
});

// Mock window.matchMedia for responsive design tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
