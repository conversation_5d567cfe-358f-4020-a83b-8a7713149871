Metadata-Version: 2.4
Name: zipp
Version: 3.22.0
Summary: Backport of pathlib-compatible object wrapper for zip files
Author-email: "<PERSON>" <<EMAIL>>
License-Expression: MIT
Project-URL: Source, https://github.com/jaraco/zipp
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: test
Requires-Dist: pytest!=8.1.*,>=6; extra == "test"
Requires-Dist: jaraco.itertools; extra == "test"
Requires-Dist: jaraco.functools; extra == "test"
Requires-Dist: more_itertools; extra == "test"
Requires-Dist: big-O; extra == "test"
Requires-Dist: pytest-ignore-flaky; extra == "test"
Requires-Dist: jaraco.test; extra == "test"
Requires-Dist: importlib_resources; python_version < "3.9" and extra == "test"
Provides-Extra: doc
Requires-Dist: sphinx>=3.5; extra == "doc"
Requires-Dist: jaraco.packaging>=9.3; extra == "doc"
Requires-Dist: rst.linker>=1.9; extra == "doc"
Requires-Dist: furo; extra == "doc"
Requires-Dist: sphinx-lint; extra == "doc"
Requires-Dist: jaraco.tidelift>=1.4; extra == "doc"
Provides-Extra: check
Requires-Dist: pytest-checkdocs>=2.4; extra == "check"
Requires-Dist: pytest-ruff>=0.2.1; sys_platform != "cygwin" and extra == "check"
Provides-Extra: cover
Requires-Dist: pytest-cov; extra == "cover"
Provides-Extra: enabler
Requires-Dist: pytest-enabler>=2.2; extra == "enabler"
Provides-Extra: type
Requires-Dist: pytest-mypy; extra == "type"
Dynamic: license-file

.. image:: https://img.shields.io/pypi/v/zipp.svg
   :target: https://pypi.org/project/zipp

.. image:: https://img.shields.io/pypi/pyversions/zipp.svg

.. image:: https://github.com/jaraco/zipp/actions/workflows/main.yml/badge.svg
   :target: https://github.com/jaraco/zipp/actions?query=workflow%3A%22tests%22
   :alt: tests

.. image:: https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json
    :target: https://github.com/astral-sh/ruff
    :alt: Ruff

.. image:: https://readthedocs.org/projects/zipp/badge/?version=latest
..    :target: https://zipp.readthedocs.io/en/latest/?badge=latest

.. image:: https://img.shields.io/badge/skeleton-2025-informational
   :target: https://blog.jaraco.com/skeleton

.. image:: https://tidelift.com/badges/package/pypi/zipp
   :target: https://tidelift.com/subscription/pkg/pypi-zipp?utm_source=pypi-zipp&utm_medium=readme


A pathlib-compatible Zipfile object wrapper. Official backport of the standard library
`Path object <https://docs.python.org/3.8/library/zipfile.html#path-objects>`_.


Compatibility
=============

New features are introduced in this third-party library and later merged
into CPython. The following table indicates which versions of this library
were contributed to different versions in the standard library:

.. list-table::
   :header-rows: 1

   * - zipp
     - stdlib
   * - 3.18
     - 3.13
   * - 3.16
     - 3.12
   * - 3.5
     - 3.11
   * - 3.2
     - 3.10
   * - 3.3 ??
     - 3.9
   * - 1.0
     - 3.8


Usage
=====

Use ``zipp.Path`` in place of ``zipfile.Path`` on any Python.

For Enterprise
==============

Available as part of the Tidelift Subscription.

This project and the maintainers of thousands of other packages are working with Tidelift to deliver one enterprise subscription that covers all of the open source you use.

`Learn more <https://tidelift.com/subscription/pkg/pypi-zipp?utm_source=pypi-zipp&utm_medium=referral&utm_campaign=github>`_.
